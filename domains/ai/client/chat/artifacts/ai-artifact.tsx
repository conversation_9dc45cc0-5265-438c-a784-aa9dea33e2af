'use client';

import type { Message, ToolInvocation } from '@ai-sdk/ui-utils';
import type { SxProps } from '@mui/joy/styles/types';
import { get } from 'lodash';
import React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import type { INodeIconValue } from '@bika/types/node/bo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { IconButton } from '@bika/ui/button';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { NodeIcon } from '@bika/ui/node/icon';
import { Typography } from '@bika/ui/texts';
import { AIArtifactByAIUI } from './ai-artifact-ai-ui';
import { DefaultArtifact } from './default-artifact';
import { AIArtifactServer } from '../../../../ai-artifacts/ai-server-artifact-ui';
import { AISkillsetClientRegistry } from '../../../../ai-skillset/client-registry';
import type { ToolResultWithError } from '../tools/type';

export interface IArtifactProps {
  message: Message;
  skillsets: SkillsetSelectDTO[];
  tool: ToolInvocation;
  isModal?: boolean;
  onClickClose: () => void;
  sx?: SxProps;
  isCopilot?: boolean;
}

interface WindowProps {
  onClickClose: () => void;
  children: React.ReactNode;
  toolConfig?: {
    roleName: string;
    subRoleName?: string;
    roleState?: string;
  };
  icon?: INodeIconValue;
  sx?: SxProps;
}

function Window(props: WindowProps) {
  const { roleName, subRoleName } = props.toolConfig || {};

  const defaultIcon = React.useMemo(
    () => props.icon || defaultIcon({
      kind: 'icon' as const,
      icon: 'components/service_outlined',
    }),
    [],
  );

  return (
    <Stack
      direction={'column'}
      sx={{
        border: '1px solid var(--border-default)',
        width: 400,
        height: 'calc(100% - 50px)',
        borderRadius: '8px',
        background: 'var(--bg-surface)',
        minWidth: '374px',
        boxShadow: 'var(--shadow-high)',
        mb: 1,
        ...props.sx,
      }}
    >
      <Box
        px={3}
        py={2}
        justifyContent="space-between"
        alignItems="center"
        display="flex"
        borderBottom="1px solid var(--border-default)"
      >
        {props.toolConfig && (
          <Stack direction="row" gap={2} alignItems="center">
            {/* {path ? (
              <AvatarImg
                avatar={{
                  type: 'PRESET',
                  url: path,
                }}
                customSize={AvatarSize.Size40}
              />
            ) : ( */}
            <NodeIcon value={defaultIcon} size={40} />
            <Box>
              <Typography level="b2" textColor="var(--text-primary)">
                {roleName}
              </Typography>
              {subRoleName && (
                <Typography level="b4" textColor="var(--text-secondary)">
                  {subRoleName}
                </Typography>
              )}
            </Box>
          </Stack>
        )}

        <IconButton
          onClick={() => {
            props.onClickClose();
          }}
        >
          <CloseOutlined color="var(--text-primary)" />
        </IconButton>
      </Box>

      <Box display="flex" px={3} py={2} flex={1} sx={{ overflow: 'auto' }}>
        {props.children}
      </Box>
    </Stack>
  );
}

function AIChatArtifactBase(props: IArtifactProps) {
  const localeContext = useLocale();
  const { t } = localeContext;

  const skillsetUIMap = React.useMemo(
    () => AISkillsetClientRegistry.getManySkillsetUI(props.skillsets),
    [props.skillsets],
  );

  const toolUIConfig = skillsetUIMap[props.tool.toolName];
  console.log('🚀 ~ AIChatArtifactBase ~ toolUIConfig:', toolUIConfig);

  const toolConfig = {
    roleName: props.tool.toolName,
    subRoleName: props.tool.state === 'result' ? t.ai.completed : t.ai.in_progress,
  };

  const toolIcon = undefined; // useToolIcon({ skillsets: props.skillsets, toolInvocation: props.tool });

  const Wrapper = props.isCopilot
    ? Box
    : (windowProps: Omit<WindowProps, 'toolConfig'>) => (
        <Window {...windowProps} toolConfig={toolConfig} icon={toolIcon} />
      );

  const result = get(props.tool, 'result') as ToolResultWithError<unknown> | undefined;

  if (result?.error) {
    return (
      <Wrapper onClickClose={props.onClickClose} sx={props.sx}>
        <Typography level="b4" textColor="'var(--status-danger)">
          {result.error.message}
        </Typography>
      </Wrapper>
    );
  }

  if (!toolUIConfig || toolUIConfig.artifact === undefined) {
    return (
      <Wrapper onClickClose={props.onClickClose} sx={props.sx}>
        <DefaultArtifact resources={props.tool || {}} tool={props.tool} skillsets={props.skillsets} />
      </Wrapper>
    );
  }

  if (typeof toolUIConfig.artifact === 'string') {
    if (toolUIConfig.artifact === 'ai-ui') {
      return (
        <Wrapper onClickClose={props.onClickClose} sx={props.sx}>
          <AIArtifactByAIUI {...props} />
        </Wrapper>
      );
    }
    if (toolUIConfig.artifact === 'server-artifact') {
      return (
        <Wrapper onClickClose={props.onClickClose} sx={props.sx}>
          <AIArtifactServer {...props} />
        </Wrapper>
      );
    }
  } else {
    // UI Component
    return (
      <Wrapper onClickClose={props.onClickClose} sx={props.sx}>
        <toolUIConfig.artifact toolInvocation={props.tool} localeContext={localeContext} skillsets={props.skillsets} />
      </Wrapper>
    );
  }
}

export const AIChatArtifact = React.memo(AIChatArtifactBase, (prevProps, nextProps) => {
  if (prevProps.tool.toolCallId !== nextProps.tool.toolCallId) {
    return false;
  }
  if (prevProps.isCopilot !== nextProps.isCopilot) {
    return false;
  }
  if (prevProps.tool !== nextProps.tool) {
    return false;
  }
  return true;
});
